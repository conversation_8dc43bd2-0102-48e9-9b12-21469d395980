import { <PERSON><PERSON>, Posi<PERSON> } from "@xyflow/react"
import { FileText, AlertCircle, FormInput, Database } from "lucide-react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { useUsers } from "@/hooks/useUsers"

function TaskNode({ data, isConnectable }) {
  const isActive = data.isActive || false
  const isCompleted = data.isCompleted || false
  const isWaiting = data.isWaiting || false
  const isSkipped = data.isSkipped || false
  const { users } = useUsers()
  const assignedUser = users.find(u => u.id === data.assignee)

  return (
    <div
      className={`px-4 py-2 shadow-md rounded-md border-2 transition-all duration-300
        ${isWaiting ? "bg-purple-100 border-purple-500 shadow-purple-200 animate-pulse" :
          isActive ? "bg-yellow-100 border-yellow-500 shadow-yellow-200 animate-pulse" :
          isCompleted ? "bg-green-50 border-green-500" :
          isSkipped ? "bg-gray-50 border-gray-300" : "bg-white border-blue-500"}
        ${data.critical ? "border-red-500" :
          isWaiting ? "border-purple-500" :
          isActive ? "border-yellow-500" :
          isCompleted ? "border-green-500" :
          isSkipped ? "border-gray-300" : "border-blue-500"}`}
    >
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isWaiting ? "bg-purple-600" : isActive ? "bg-yellow-600" : isCompleted ? "bg-green-600" : isSkipped ? "bg-gray-400" : "bg-blue-500"}`}
      />
      <div className="flex items-center">
        <FileText className={`mr-2 h-5 w-5 ${isWaiting ? "text-purple-600" : isActive ? "text-yellow-600" : isCompleted ? "text-green-600" : isSkipped ? "text-gray-400" : "text-blue-500"}`} />
        <div className="font-bold">{data.label}</div>
        {data.critical && <AlertCircle className="ml-2 h-4 w-4 text-red-500" />}
        {isWaiting && <span className="ml-2 text-xs bg-purple-200 text-purple-800 px-1 py-0.5 rounded animate-pulse">Waiting</span>}
        {isActive && !isWaiting && <span className="ml-2 text-xs bg-yellow-200 text-yellow-800 px-1 py-0.5 rounded animate-pulse">Active</span>}
        {isCompleted && <span className="ml-2 text-xs bg-green-200 text-green-800 px-1 py-0.5 rounded">Completed</span>}
        {isSkipped && <span className="ml-2 text-xs bg-gray-200 text-gray-800 px-1 py-0.5 rounded">Skipped</span>}
      </div>
      {data.description && <div className="mt-1 text-xs text-gray-500">{data.description}</div>}

      {/* Display form fields indicator if present */}
      {data.formFields && data.formFields.length > 0 && (
        <div className="mt-1 flex items-center gap-1 text-xs text-blue-600">
          <FormInput className="h-3 w-3" />
          <span>{data.formFields.length} form {data.formFields.length === 1 ? 'field' : 'fields'}</span>
        </div>
      )}

      {/* Display selected contextual data with details if present */}
      {data.selectedOutputs && data.selectedOutputs.length > 0 && (
        <div className="mt-2">
          <div className="text-xs font-semibold text-gray-600">Contextual Data:</div>
          <div className="text-xs mt-1 pl-2 border-l-2 border-blue-200">
            {data.selectedOutputs.slice(0, 2).map((selection: any, index: number) => (
              <div key={index} className="mb-1">
                <span className="font-medium">{selection.label}</span>
                <div className="text-blue-600 bg-blue-100 px-1 py-0.5 rounded text-xs inline-block ml-1">
                  {`{${selection.nodeId}.${selection.key}}`}
                </div>
              </div>
            ))}
            {data.selectedOutputs.length > 2 && (
              <div className="text-gray-400">+ {data.selectedOutputs.length - 2} more variables</div>
            )}
          </div>
        </div>
      )}

      {/* Display legacy contextual variables indicator if present (backward compatibility) */}
      {data.selectedContextVars && data.selectedContextVars.length > 0 && !data.selectedOutputs && (
        <div className="mt-1 flex items-center gap-1 text-xs text-emerald-600">
          <Database className="h-3 w-3" />
          <span>{data.selectedContextVars.length} context {data.selectedContextVars.length === 1 ? 'variable' : 'variables'}</span>
        </div>
      )}

      {assignedUser && (
        <div className="mt-2 flex items-center gap-2 text-xs bg-blue-50 p-1 rounded">
          <Avatar className="h-4 w-4">
            <AvatarFallback>{assignedUser.firstName.charAt(0)}</AvatarFallback>
          </Avatar>
          <span>{assignedUser.firstName}</span>
        </div>
      )}
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className={`w-3 h-3 ${isWaiting ? "bg-purple-600" : isActive ? "bg-yellow-600" : isCompleted ? "bg-green-600" : isSkipped ? "bg-gray-400" : "bg-blue-500"}`}
      />
    </div>
  )
}

export { TaskNode }

