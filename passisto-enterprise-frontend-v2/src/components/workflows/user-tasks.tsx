"use client"

import { useState } from "react"
import { useUserTasks } from "@/hooks/useUserTasks"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, AlertCircle, CheckCircle2, Clock, Paperclip, Database } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"

// Simple markdown rendering function
const renderMarkdown = (text: string): React.ReactNode => {
  if (!text) return null;

  // Process the text in stages
  let processedText = text;

  // Convert markdown to HTML elements
  // Handle headings
  processedText = processedText.replace(/^### (.+)$/gm, '<h3 class="text-lg font-semibold mt-3 mb-2">$1</h3>');
  processedText = processedText.replace(/^## (.+)$/gm, '<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>');
  processedText = processedText.replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold mt-4 mb-3">$1</h1>');

  // Handle bold and italic
  processedText = processedText.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
  processedText = processedText.replace(/\*(.+?)\*/g, '<em>$1</em>');
  processedText = processedText.replace(/_(.+?)_/g, '<em>$1</em>');

  // Handle lists
  processedText = processedText.replace(/^\* (.+)$/gm, '<li class="ml-4">$1</li>');
  processedText = processedText.replace(/^\d+\. (.+)$/gm, '<li class="ml-4 list-decimal">$1</li>');

  // Handle links
  processedText = processedText.replace(/\[(.+?)\]\((.+?)\)/g, '<a href="$2" class="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>');

  // Handle code blocks and inline code
  processedText = processedText.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-2 rounded my-2 overflow-x-auto"><code>$1</code></pre>');
  processedText = processedText.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>');

  // Handle paragraphs - wrap text that's not already in HTML tags
  processedText = processedText.replace(/^(?!<[a-z]).+$/gm, '<p class="mb-2">$&</p>');

  // Handle line breaks
  processedText = processedText.replace(/\n/g, '<br />');

  // Return as dangerouslySetInnerHTML
  return <div dangerouslySetInnerHTML={{ __html: processedText }} />;
};
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileUpload } from "@/components/workflows/file-upload"
import { FileAttachments } from "@/components/workflows/file-attachments"
import { FormField, UserTask } from "@/services/userTaskApi"

export function UserTasks() {
  const { tasks, loading, error, completeTask } = useUserTasks()
  const [selectedTask, setSelectedTask] = useState<UserTask | null>(null)
  const [taskComment, setTaskComment] = useState("")
  const [formValues, setFormValues] = useState<Record<string, any>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Reset form values when a new task is selected
  const handleSelectTask = (task: UserTask) => {
    setSelectedTask(task)
    setFormValues({})
    setTaskComment("")
  }

  // Handle form field changes
  const handleFormFieldChange = (fieldId: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [fieldId]: value
    }))
  }

  const handleCompleteTask = async () => {
    if (!selectedTask) return

    setIsSubmitting(true)
    try {
      // Combine form values with comment
      const taskData = {
        ...formValues,
        comment: taskComment,
        completedAt: new Date().toISOString()
      }

      await completeTask(selectedTask.workflowRunId, selectedTask.nodeId, taskData)
      setSelectedTask(null)
      setTaskComment("")
      setFormValues({})
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading your tasks...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 bg-destructive/10 border border-destructive rounded-md">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-destructive mr-2" />
          <p className="text-destructive">Error loading tasks: {error}</p>
        </div>
      </div>
    )
  }

  if (tasks.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <CheckCircle2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">You don't have any pending tasks.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Your Tasks ({tasks.length})</h2>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {tasks.map((task) => (
          <Card key={`${task.workflowRunId}-${task.nodeId}`} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{task.nodeLabel}</CardTitle>
                {task.nodeCritical && (
                  <Badge variant="destructive" className="ml-2">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Critical
                  </Badge>
                )}
              </div>
              <CardDescription>
                From workflow: {task.workflowName}
              </CardDescription>
            </CardHeader>

            <CardContent className="pb-2">
              {task.nodeDescription && (
                <p className="text-sm text-muted-foreground mb-2">{task.nodeDescription}</p>
              )}

              {/* Show contextual data preview in the card */}
              {task.contextualData && Object.keys(task.contextualData).length > 0 && (
                <div className="mb-2 p-2 bg-blue-50 rounded-md border border-blue-100">
                  <div className="flex items-center gap-1 text-xs text-blue-700 mb-1">
                    <Database className="h-3 w-3" />
                    <span className="font-medium">Contextual data:</span>
                  </div>
                  <div className="space-y-1">
                    {Object.keys(task.contextualData).map(key => (
                      <div key={key} className="text-xs">
                        <span className="font-medium">{key}:</span>{' '}
                        <span className="text-muted-foreground text-preview block">
                          {typeof task.contextualData![key] === 'object' ? (
                            <div className="markdown-preview">
                              {JSON.stringify(task.contextualData![key]).substring(0, 50) + '...'}
                            </div>
                          ) : (
                            <div className="markdown-preview">
                              {/* Create a simplified preview of the markdown content */}
                              {String(task.contextualData![key])
                                .replace(/[#*_`[\]()]/g, '') // Remove markdown syntax
                                .substring(0, 50)}
                              {String(task.contextualData![key]).length > 50 && '...'}
                            </div>
                          )}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex items-center text-xs text-muted-foreground">
                <Clock className="h-3 w-3 mr-1" />
                Assigned {formatDistanceToNow(new Date(task.assignedAt), { addSuffix: true })}
              </div>
            </CardContent>

            <CardFooter>
              <Button
                className="w-full"
                onClick={() => handleSelectTask(task)}
              >
                Complete Task
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Task completion dialog */}
      <Dialog open={!!selectedTask} onOpenChange={(open) => !open && setSelectedTask(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Complete Task: {selectedTask?.nodeLabel}</DialogTitle>
            <DialogDescription>
              Add any comments or notes before completing this task.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Display contextual data from previous nodes if available */}
            {selectedTask?.contextualData && Object.keys(selectedTask.contextualData).length > 0 && (
              <div className="space-y-2 border p-4 rounded-md bg-blue-50 mb-4">
                <h3 className="font-medium flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Contextual Data
                </h3>
                <div className="space-y-2">
                  {Object.entries(selectedTask.contextualData).map(([key, value]) => (
                    <div key={key} className="bg-white p-2 rounded border">
                      <div className="text-sm font-medium">{key}</div>
                      <div className="text-sm whitespace-pre-wrap overflow-auto max-h-40 markdown-content">
                        {typeof value === 'object'
                          ? JSON.stringify(value, null, 2)
                          : renderMarkdown(String(value))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Render form fields if they exist */}
            {selectedTask?.formFields && selectedTask.formFields.length > 0 && (
              <div className="space-y-4 border p-4 rounded-md bg-slate-50">
                <h3 className="font-medium">Task Form</h3>
                {selectedTask.formFields.map((field: FormField) => (
                  <div key={field.id} className="space-y-2">
                    <Label htmlFor={field.id}>
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>

                    {field.type === 'text' && (
                      <Input
                        id={field.id}
                        placeholder={field.placeholder ?? ''}
                        value={formValues[field.id] ?? ''}
                        onChange={(e) => handleFormFieldChange(field.id, e.target.value)}
                        required={field.required}
                      />
                    )}

                    {field.type === 'textarea' && (
                      <Textarea
                        id={field.id}
                        placeholder={field.placeholder ?? ''}
                        value={formValues[field.id] ?? ''}
                        onChange={(e) => handleFormFieldChange(field.id, e.target.value)}
                        required={field.required}
                      />
                    )}

                    {field.type === 'select' && field.options && (
                      <Select
                        value={formValues[field.id] ?? ''}
                        onValueChange={(value) => handleFormFieldChange(field.id, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select an option" />
                        </SelectTrigger>
                        <SelectContent>
                          {field.options.map((option: { value: string; label: string }) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}

                    {field.type === 'checkbox' && (
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={field.id}
                          checked={formValues[field.id] ?? false}
                          onCheckedChange={(checked) => handleFormFieldChange(field.id, checked)}
                        />
                        <label
                          htmlFor={field.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {field.label}
                        </label>
                      </div>
                    )}

                    {field.type === 'date' && (
                      <Input
                        id={field.id}
                        type="date"
                        value={formValues[field.id] ?? ''}
                        onChange={(e) => handleFormFieldChange(field.id, e.target.value)}
                        required={field.required}
                      />
                    )}
                  </div>
                ))}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="comment">Comments (optional)</Label>
              <Textarea
                id="comment"
                placeholder="Add any notes or comments about this task..."
                value={taskComment}
                onChange={(e) => setTaskComment(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <Paperclip className="h-4 w-4" />
                Attachments
              </Label>
              <FileUpload
                nodeRunId={selectedTask ? `${selectedTask.workflowRunId}-${selectedTask.nodeId}` : ""}
                onFileUploaded={() => {}}
              />

              {selectedTask && (
                <FileAttachments
                  nodeRunId={`${selectedTask.workflowRunId}-${selectedTask.nodeId}`}
                  className="mt-4"
                />
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setSelectedTask(null)}>
              Cancel
            </Button>
            <Button onClick={handleCompleteTask} disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Complete Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
